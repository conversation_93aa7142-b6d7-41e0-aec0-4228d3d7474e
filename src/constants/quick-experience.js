// 快速体验页面常量配置
export const QUICK_EXPERIENCE_CONFIG = {
  // 图片类型
  IMAGE_TYPES: {
    ANSWER: 'answer',
    PAPER: 'paper'
  },
  
  // 选择状态
  SELECTION_STATE: {
    NONE: -1,
    PRESET: 0
  },
  
  // 处理状态
  PROCESS_STATE: {
    IDLE: 'idle',
    PROCESSING: 'processing',
    COMPLETED: 'completed'
  },
  
  // 错误消息
  ERROR_MESSAGES: {
    CHOOSE_IMAGE_FAILED: '选择图片失败',
    UPLOAD_FAILED: '图片上传失败',
    CORRECTION_FAILED: '批改失败，请重试',
    API_CALL_FAILED: 'API调用失败，请检查网络连接',
    DATA_PARSE_FAILED: '分析结果解析失败',
    JSON_FORMAT_ERROR: '返回数据格式错误',
    AUTHORIZATION_ERROR: 'API Key验证失败'
  },
  
  // 成功消息
  SUCCESS_MESSAGES: {
    CORRECTION_COMPLETED: '批改完成！',
    API_KEY_SET: 'API Key已设置'
  },
  
  // UI 配置
  UI_CONFIG: {
    TOAST_DURATION: 3000,
    API_KEY_PROMPT_DELAY: 2000,
    IMAGE_MAX_HEIGHT: 400,
    COMPARE_IMAGE_MAX_HEIGHT: 300
  },
  
  // 默认学生信息
  DEFAULT_STUDENT: {
    name: '体验学生',
    id: 'DEMO001'
  }
}

// 图片处理工具函数
export const ImageUtils = {
  // 检查是否为网络图片
  isNetworkImage(url) {
    return url && url.startsWith('http')
  },
  
  // 检查是否为本地临时文件
  isLocalTempFile(url) {
    return url && !url.startsWith('http')
  }
}

// 验证工具函数
export const ValidationUtils = {
  // 验证是否可以开始批改
  canStartCorrection(answerUrl, paperUrl) {
    return answerUrl && paperUrl
  },
  
  // 验证API响应
  isValidApiResponse(response) {
    return response && response.code === 200 && response.data
  }
}
