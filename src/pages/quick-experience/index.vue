<template>
  <view class="quick-experience">
    <view class="content-container">
      <!-- 页面标题 -->
      <view class="page-header">
        <text class="page-title">快速体验批改</text>
        <text class="page-subtitle">体验AI智能批改效果</text>
      </view>

      <!-- 图片选择区域 -->
      <view class="image-selection-section" v-if="!showResult">
        <!-- 参考答案区域 -->
        <view class="image-group">
          <text class="group-title">参考答案</text>
          <view class="image-options">
            <view 
              class="preset-item" 
              :class="{ 'selected': selectedAnswer === 0 }"
              @click="selectAnswer(0)"
            >
              <image :src="presetAnswer.url" mode="aspectFit" class="preset-image"></image>
              <text class="preset-label">{{ presetAnswer.label }}</text>
            </view>
            <view class="upload-btn" @click="uploadAnswerImage">
              <uni-icons type="camera" size="24" color="#4285F4"></uni-icons>
              <text>上传图片</text>
            </view>
          </view>
          <view class="selected-image" v-if="answerImageUrl">
            <image :src="answerImageUrl" mode="aspectFit" class="selected-img"></image>
          </view>
        </view>

        <!-- 学生试卷区域 -->
        <view class="image-group">
          <text class="group-title">学生试卷</text>
          <view class="image-options">
            <view 
              class="preset-item" 
              :class="{ 'selected': selectedPaper === 0 }"
              @click="selectPaper(0)"
            >
              <image :src="presetPaper.url" mode="aspectFit" class="preset-image"></image>
              <text class="preset-label">{{ presetPaper.label }}</text>
            </view>
            <view class="upload-btn" @click="uploadPaperImage">
              <uni-icons type="camera" size="24" color="#4285F4"></uni-icons>
              <text>上传图片</text>
            </view>
          </view>
          <view class="selected-image" v-if="paperImageUrl">
            <image :src="paperImageUrl" mode="aspectFit" class="selected-img"></image>
          </view>
        </view>

        <!-- 开始批改按钮 -->
        <view class="action-section">
          <button 
            class="start-correction-btn" 
            :disabled="!canStartCorrection"
            :class="{ 'disabled': !canStartCorrection }"
            @click="startCorrection"
          >
            <text v-if="!isProcessing">开始AI批改</text>
            <view v-else class="processing-content">
              <view class="loading-spinner">
                <uni-icons type="refresh" size="20" color="#fff"></uni-icons>
              </view>
              <text>AI批改中...</text>
            </view>
          </button>
        </view>
      </view>

      <!-- 批改结果展示区域 -->
      <view class="result-section" v-if="showResult">
        <!-- 学生信息 -->
        <view class="student-info">
          <view class="info-left">
            <view class="student-details">
              <view class="student-name">{{ mockResult.student || '体验学生' }}</view>
              <view class="student-id">学号：DEMO001</view>
            </view>
          </view>
          <view class="info-right">
            <text class="exam-date">{{ currentDate }}</text>
          </view>
        </view>

        <!-- 成绩统计 -->
        <view class="score-stats">
          <view class="stat-item total-score">
            <text class="stat-value total">{{ mockResult.correctRate }}%</text>
            <text class="stat-label">正确率</text>
          </view>
          <view class="stat-item correct-count">
            <text class="stat-value correct">{{ mockResult.correctCount }}</text>
            <text class="stat-label">正确数</text>
          </view>
          <view class="stat-item wrong-count">
            <text class="stat-value wrong">{{ mockResult.wrongCount }}</text>
            <text class="stat-label">错误数</text>
          </view>
        </view>

        <!-- 分析建议 -->
        <view class="analysis-section">
          <view class="analysis-item error-analysis">
            <text class="analysis-title">错题分析</text>
            <view class="analysis-content">
              <view class="content-box">
                <view class="list-content">
                  <view class="list-item">
                    <text class="item-text">{{ mockResult.errorAnalysis }}</text>
                  </view>
                </view>
              </view>
            </view>
          </view>
          <view class="analysis-item improvement">
            <text class="analysis-title">改进建议</text>
            <view class="analysis-content">
              <view class="list-content">
                <view class="list-item">
                  <text class="item-text">{{ mockResult.improvementSuggestions }}</text>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 答题详情 -->
        <view class="answer-detail-section">
          <view class="section-header">
            <view class="section-title">答题详情</view>
          </view>
          <view class="answer-list">
            <view 
              class="answer-item" 
              v-for="(item, index) in mockResult.answerList" 
              :key="index"
            >
              <view class="answer-header">
                <text class="question-number">{{ index + 1 }}.</text>
                <text class="question-content">{{ item.question }}</text>
                <view class="result-badge" :class="{ 'correct': item.isCorrect, 'wrong': !item.isCorrect }">
                  <text>{{ item.isCorrect ? '✓' : '✗' }}</text>
                </view>
              </view>
              <view class="answer-content">
                <view class="answer-row">
                  <text class="label">标准答案：</text>
                  <text class="value correct-answer">{{ item.standardAnswer }}</text>
                </view>
                <view class="answer-row">
                  <text class="label">学生答案：</text>
                  <text class="value student-answer" :class="{ 'wrong': !item.isCorrect }">{{ item.studentAnswer }}</text>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 图片对比区域 -->
        <view class="image-compare-section">
          <view class="section-title">图片对比</view>
          <view class="image-compare">
            <view class="compare-item">
              <text class="compare-label">参考答案</text>
              <image :src="answerImageUrl" mode="aspectFit" class="compare-image" @click="previewImage(answerImageUrl)"></image>
            </view>
            <view class="compare-item">
              <text class="compare-label">学生试卷</text>
              <image :src="paperImageUrl" mode="aspectFit" class="compare-image" @click="previewImage(paperImageUrl)"></image>
            </view>
          </view>
        </view>

        <!-- 重新体验按钮 -->
        <view class="restart-section">
          <button class="restart-btn" @click="restartExperience">
            <text>重新体验</text>
          </button>
        </view>
      </view>
    </view>

    <LoadingMask :show="isProcessing" />
  </view>
</template>

<script setup>
import { ref, computed } from 'vue'
import LoadingMask from '@/components/LoadingMask.vue'
import { formatDate } from '@/utils/tools'
import { correctByDoubao, setArKApiKey } from '@/api/llm.js'

// 预设示例图片
const presetAnswer = ref({
  label: '参加答案',
  url: 'https://quick-marker-oss.research.top/images/8b31fb04-9e7b-475a-8f13-b86ae45bf71f.jpg'
})

const presetPaper = ref({
  label: '示例试卷',
  url: 'https://quick-marker-oss.research.top/images/04a395e8-a339-4873-9b1e-1c4bb62a0525.jpg'
})

// 选择状态 - 默认选中示例图片
const selectedAnswer = ref(0)
const selectedPaper = ref(0)
const answerImageUrl = ref('https://quick-marker-oss.research.top/images/8b31fb04-9e7b-475a-8f13-b86ae45bf71f.jpg')
const paperImageUrl = ref('https://quick-marker-oss.research.top/images/04a395e8-a339-4873-9b1e-1c4bb62a0525.jpg')

// 页面状态
const isProcessing = ref(false)
const showResult = ref(false)

// 当前日期
const currentDate = computed(() => {
  return formatDate(new Date())
})

// 模拟批改结果
const mockResult = ref({
  correctRate: 85,
  correctCount: 17,
  wrongCount: 3,
  errorAnalysis: '主要错误集中在单词拼写和语法时态上，特别是过去式的变化规则掌握不够熟练。建议加强基础语法练习，多做相关的拼写训练。',
  improvementSuggestions: '1. 加强单词记忆，特别是不规则动词的过去式；2. 多做语法练习，重点关注时态变化；3. 平时多读英文材料，培养语感；4. 建议每天坚持听写练习15-20分钟。',
  answerList: [
    {
      question: 'go (过去式)',
      standardAnswer: 'went',
      studentAnswer: 'went',
      isCorrect: true
    },
    {
      question: 'eat (过去式)',
      standardAnswer: 'ate',
      studentAnswer: 'eated',
      isCorrect: false
    },
    {
      question: 'play (过去式)',
      standardAnswer: 'played',
      studentAnswer: 'played',
      isCorrect: true
    },
    {
      question: 'run (过去式)',
      standardAnswer: 'ran',
      studentAnswer: 'runed',
      isCorrect: false
    },
    {
      question: 'take (过去式)',
      standardAnswer: 'took',
      studentAnswer: 'took',
      isCorrect: true
    }
  ]
})

// 是否可以开始批改
const canStartCorrection = computed(() => {
  return answerImageUrl.value && paperImageUrl.value
})

// 选择参考答案
const selectAnswer = (index) => {
  selectedAnswer.value = index
  answerImageUrl.value = presetAnswer.value.url
  // 如果学生试卷还没选择，自动选择示例试卷
  if (!paperImageUrl.value) {
    selectedPaper.value = 0
    paperImageUrl.value = presetPaper.value.url
  }
}

// 选择学生试卷
const selectPaper = (index) => {  
  selectedPaper.value = index
  paperImageUrl.value = presetPaper.value.url
  // 如果参考答案还没选择，自动选择示例答案
  if (!answerImageUrl.value) {
    selectedAnswer.value = 0
    answerImageUrl.value = presetAnswer.value.url
  }
}

// 上传参考答案图片
const uploadAnswerImage = () => {
  uni.chooseImage({
    count: 1,
    sizeType: ['compressed'],
    sourceType: ['camera', 'album'],
    success: (res) => {
      selectedAnswer.value = -1
      answerImageUrl.value = res.tempFilePaths[0]
    },
    fail: (err) => {
      console.error('选择图片失败:', err)
      uni.showToast({
        title: '选择图片失败',
        icon: 'none'
      })
    }
  })
}

// 上传学生试卷图片
const uploadPaperImage = () => {
  uni.chooseImage({
    count: 1,
    sizeType: ['compressed'],
    sourceType: ['camera', 'album'],
    success: (res) => {
      selectedPaper.value = -1
      paperImageUrl.value = res.tempFilePaths[0]
    },
    fail: (err) => {
      console.error('选择图片失败:', err)
      uni.showToast({
        title: '选择图片失败',
        icon: 'none'
      })
    }
  })
}

// 开始批改
const startCorrection = async () => {
  if (!canStartCorrection.value) return
  
  isProcessing.value = true
  
  try {
    // 上传图片并获取可访问的URL
    const [answerUrl, paperUrl] = await Promise.all([
      uploadImageToOSS(answerImageUrl.value),
      uploadImageToOSS(paperImageUrl.value)
    ])
    
    // 调用大模型进行批改
    const result = await correctByDoubao(answerUrl, paperUrl)
    
    if (result.code === 200) {
      // 更新批改结果
      mockResult.value = result.data
      showResult.value = true
      
      uni.showToast({
        title: '批改完成！',
        icon: 'success'
      })
    } else {
      throw new Error(result.message || '批改失败')
    }
  } catch (error) {
    console.error('批改失败:', error)
    
    // 根据错误类型显示不同提示
    let errorMsg = '批改失败，请重试'
    if (error.message.includes('API调用失败')) {
      errorMsg = 'API调用失败，请检查网络连接'
    } else if (error.message.includes('数据结构')) {
      errorMsg = '分析结果解析失败'
    } else if (error.message.includes('JSON')) {
      errorMsg = '返回数据格式错误'
    }
    
    uni.showToast({
      title: errorMsg,
      icon: 'none',
      duration: 3000
    })
    
    // 如果是API Key相关错误，提示重新设置
    if (error.message.includes('Authorization')) {
      setTimeout(() => {
        promptForApiKey()
      }, 2000)
    }
  } finally {
    isProcessing.value = false
  }
}

// 提示用户输入API Key
const promptForApiKey = async () => {
  return new Promise((resolve) => {
    uni.showModal({
      title: '设置API Key',
      content: '请输入您的豆包ARK API Key以使用AI批改功能',
      editable: true,
      placeholderText: '请输入API Key',
      success: (res) => {
        if (res.confirm && res.content) {
          setArKApiKey(res.content)
          uni.showToast({
            title: 'API Key已设置',
            icon: 'success'
          })
        }
        resolve()
      },
      fail: () => {
        resolve()
      }
    })
  })
}

// 上传图片到OSS或转换为可访问的URL
const uploadImageToOSS = async (imageUrl) => {
  try {
    // 如果是网络图片，直接返回
    if (imageUrl.startsWith('http')) {
      return imageUrl
    }
    
    // 如果是本地临时文件，需要上传到服务器
    // 这里简化处理，实际项目中应该上传到OSS或服务器
    return new Promise((resolve, reject) => {
      uni.uploadFile({
        url: 'https://your-server.com/upload', // 替换为实际的上传接口
        filePath: imageUrl,
        name: 'file',
        success: (res) => {
          const data = JSON.parse(res.data)
          if (data.code === 200) {
            resolve(data.url)
          } else {
            reject(new Error('图片上传失败'))
          }
        },
        fail: (err) => {
          console.error('上传失败，使用base64编码:', err)
          // 如果上传失败，尝试转换为base64
          convertToBase64(imageUrl)
            .then(base64 => resolve(`data:image/jpeg;base64,${base64}`))
            .catch(reject)
        }
      })
    })
  } catch (error) {
    console.error('处理图片URL失败:', error)
    throw error
  }
}

// 将本地图片转换为base64
const convertToBase64 = (filePath) => {
  return new Promise((resolve, reject) => {
    uni.getFileSystemManager().readFile({
      filePath: filePath,
      encoding: 'base64',
      success: (res) => {
        resolve(res.data)
      },
      fail: (err) => {
        console.error('读取文件失败:', err)
        reject(err)
      }
    })
  })
}

// 预览图片
const previewImage = (url) => {
  uni.previewImage({
    urls: [url],
    current: url
  })
}

// 重新体验
const restartExperience = () => {
  showResult.value = false
  selectedAnswer.value = -1
  selectedPaper.value = -1
  answerImageUrl.value = ''
  paperImageUrl.value = ''
}
</script>

<style lang="scss" scoped>
.quick-experience {
  min-height: 100vh;
  background-color: #f5f5f5;
  
  .content-container {
    padding: 20rpx;
    box-sizing: border-box;
    
    .page-header {
      text-align: center;
      padding: 40rpx 0;
      
      .page-title {
        display: block;
        font-size: 48rpx;
        font-weight: 600;
        color: #333;
        margin-bottom: 16rpx;
      }
      
      .page-subtitle {
        display: block;
        font-size: 28rpx;
        color: #666;
      }
    }
    
    .image-selection-section {
      .image-group {
        background-color: #fff;
        border-radius: 12rpx;
        padding: 30rpx;
        margin-bottom: 20rpx;
        
        .group-title {
          display: block;
          font-size: 32rpx;
          font-weight: 600;
          color: #333;
          margin-bottom: 20rpx;
        }
        
        .image-options {
          display: flex;
          gap: 20rpx;
          margin-bottom: 20rpx;
          
          .preset-item {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20rpx;
            border: 2rpx solid #E5E5E5;
            border-radius: 12rpx;
            transition: all 0.3s ease;
            
            &.selected {
              border-color: #4285F4;
              background-color: #F0F7FF;
            }
            
            .preset-image {
              width: 200rpx;
              height: 200rpx;
              border-radius: 8rpx;
              margin-bottom: 12rpx;
            }
            
            .preset-label {
              font-size: 24rpx;
              color: #666;
            }
          }
          
          .upload-btn {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12rpx;
            padding: 20rpx;
            border: 2rpx dashed #4285F4;
            border-radius: 12rpx;
            color: #4285F4;
            
            text {
              font-size: 28rpx;
            }
            
            &:active {
              opacity: 0.8;
            }
          }
        }
        
        .selected-image {
          margin-top: 20rpx;
          text-align: center;
          
          .selected-img {
            max-width: 100%;
            max-height: 400rpx;
            border-radius: 12rpx;
          }
        }
      }
      
      .action-section {
        padding: 40rpx 0;
        
        .start-correction-btn {
          width: 100%;
          height: 88rpx;
          background: linear-gradient(135deg, #4285F4 0%, #34A853 100%);
          border-radius: 12rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #fff;
          font-size: 32rpx;
          font-weight: 600;
          border: none;
          
          &.disabled {
            background: #E5E5E5;
            color: #999;
          }
          
          .processing-content {
            display: flex;
            align-items: center;
            gap: 12rpx;
            
            .loading-spinner {
              animation: spin 1s linear infinite;
            }
          }
          
          &:not(.disabled):active {
            opacity: 0.9;
            transform: scale(0.98);
          }
        }
      }
    }
    
    // 复用详情页面的样式
    .student-info {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      background-color: #fff;
      padding: 30rpx;
      border-radius: 12rpx;
      margin-bottom: 20rpx;

      .info-left {
        .student-details {
          .student-name {
            font-size: 32rpx;
            color: #333;
            margin-bottom: 8rpx;
          }

          .student-id {
            font-size: 26rpx;
            color: #666;
          }
        }
      }

      .info-right {
        .exam-date {
          font-size: 26rpx;
          color: #666;
        }
      }
    }

    .score-stats {
      display: flex;
      justify-content: space-between;
      background-color: #fff;
      padding: 30rpx;
      border-radius: 12rpx;
      margin-bottom: 20rpx;

      .stat-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        flex: 1;

        .stat-value {
          font-size: 50rpx;
          font-weight: 600;
          margin-bottom: 8rpx;

          &.total {
            color: #4285F4;
          }

          &.correct {
            color: #4CAF50;
          }

          &.wrong {
            color: #FF5252;
          }
        }

        .stat-label {
          font-size: 26rpx;
          color: #666;
        }
      }
    }

    .analysis-section {
      background-color: #fff;
      padding: 30rpx;
      border-radius: 12rpx;
      margin-bottom: 20rpx;

      .analysis-item {
        margin-bottom: 30rpx;

        &:last-child {
          margin-bottom: 0;
        }

        .analysis-title {
          font-size: 32rpx;
          font-weight: 600;
          color: #333;
          margin-bottom: 20rpx;
          display: block;
        }

        .analysis-content {
          .content-box {
            background-color: #FFF7F5;
            border-radius: 8rpx;
            padding: 20rpx;
          }

          .list-content {
            padding: 0 10rpx;

            .list-item {
              display: flex;
              margin-bottom: 16rpx;
              font-size: 28rpx;
              line-height: 1.5;

              &:last-child {
                margin-bottom: 0;
              }

              .item-text {
                color: #333;
                flex: 1;
              }
            }
          }
        }

        &.error-analysis {
          .content-box {
            background-color: #FFF7F5;
          }
        }

        &.improvement {
          .list-content {
            background-color: #F0F7FF;
            border-radius: 8rpx;
            padding: 20rpx;
          }
        }
      }
    }

    .answer-detail-section {
      background-color: #fff;
      padding: 30rpx;
      border-radius: 12rpx;
      margin-bottom: 20rpx;

      .section-header {
        margin-bottom: 20rpx;

        .section-title {
          font-size: 32rpx;
          font-weight: 600;
          color: #333;
        }
      }
      
      .answer-list {
        .answer-item {
          padding: 20rpx 0;
          border-bottom: 1px solid #F5F5F5;
          
          &:last-child {
            border-bottom: none;
          }
          
          .answer-header {
            display: flex;
            align-items: center;
            margin-bottom: 12rpx;
            
            .question-number {
              font-size: 28rpx;
              color: #666;
              margin-right: 8rpx;
              min-width: 40rpx;
            }
            
            .question-content {
              flex: 1;
              font-size: 28rpx;
              color: #333;
            }
            
            .result-badge {
              width: 40rpx;
              height: 40rpx;
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 20rpx;
              font-weight: bold;
              
              &.correct {
                background-color: #4CAF50;
                color: #fff;
              }
              
              &.wrong {
                background-color: #FF5252;
                color: #fff;
              }
            }
          }
          
          .answer-content {
            margin-left: 48rpx;
            
            .answer-row {
              display: flex;
              margin-bottom: 8rpx;
              
              &:last-child {
                margin-bottom: 0;
              }
              
              .label {
                font-size: 26rpx;
                color: #666;
                min-width: 120rpx;
              }
              
              .value {
                font-size: 26rpx;
                
                &.correct-answer {
                  color: #4CAF50;
                }
                
                &.student-answer {
                  color: #333;
                  
                  &.wrong {
                    color: #FF5252;
                  }
                }
              }
            }
          }
        }
      }
    }
    
    .image-compare-section {
      background-color: #fff;
      padding: 30rpx;
      border-radius: 12rpx;
      margin-bottom: 20rpx;
      
      .section-title {
        font-size: 32rpx;
        font-weight: 600;
        color: #333;
        margin-bottom: 20rpx;
        display: block;
      }
      
      .image-compare {
        display: flex;
        gap: 20rpx;
        
        .compare-item {
          flex: 1;
          text-align: center;
          
          .compare-label {
            display: block;
            font-size: 28rpx;
            color: #666;
            margin-bottom: 12rpx;
          }
          
          .compare-image {
            width: 100%;
            max-height: 300rpx;
            border-radius: 8rpx;
            border: 1px solid #E5E5E5;
          }
        }
      }
    }
    
    .restart-section {
      padding: 40rpx 0;
      
      .restart-btn {
        width: 100%;
        height: 88rpx;
        background-color: #fff;
        border: 2rpx solid #4285F4;
        border-radius: 12rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #4285F4;
        font-size: 32rpx;
        
        &:active {
          opacity: 0.8;
          background-color: #F0F7FF;
        }
      }
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
